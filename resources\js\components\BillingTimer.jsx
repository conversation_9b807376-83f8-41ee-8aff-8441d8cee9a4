import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
    fetchOrganizationBillingStatus,
    selectBillingStatus,
    selectOrganizationLoading,
    selectOrganizationError,
} from '../store/slices/organizationSlice';
import {
    selectIsAuthenticated,
    selectUser,
} from '../store/slices/authSlice';

const ClockIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto">
        <path
            d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
            stroke="#DC3545"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
            stroke="#DC3545"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

const BillingTimer = ({ variant = 'main' }) => {
    const dispatch = useDispatch();
    const billingStatus = useSelector(selectBillingStatus);
    const loading = useSelector(selectOrganizationLoading);
    const error = useSelector(selectOrganizationError);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    const user = useSelector(selectUser);

    useEffect(() => {
        console.log('BillingTimer: Authentication status:', isAuthenticated);
        console.log('BillingTimer: User:', user);

        if (isAuthenticated && user) {
            console.log('BillingTimer: Fetching organization billing status...');
            dispatch(fetchOrganizationBillingStatus());
        } else {
            console.log('BillingTimer: User not authenticated, skipping fetch');
        }
    }, [dispatch, isAuthenticated, user]);

    // Don't render anything if loading, error, or no billing status
    if (loading) {
        console.log('BillingTimer: Loading...');
        return null;
    }

    if (error) {
        console.error('BillingTimer: Error:', error);
        return null;
    }

    if (!billingStatus) {
        console.log('BillingTimer: No billing status data');
        return null;
    }

    console.log('BillingTimer: Billing status:', billingStatus);

    // Don't render if user is subscribed and subscription is not canceled
    if (billingStatus.is_subscribed && !billingStatus.subscription_canceled) {
        return null;
    }

    const getVariantClasses = () => {
        switch (variant) {
            case 'product':
                return 'billing-custom-css_product';
            case 'main':
            default:
                return 'billing-custom-css_main billing-custom-css-main-menu';
        }
    };

    const renderContent = () => {
        // Case 1: User is on trial
        if (billingStatus.on_trial) {
            return (
                <>
                    <h3 className="text-center"><b>Trial Plan</b></h3>
                    <p className="mb-3 text-center">
                        {billingStatus.days_left_in_trial !== null && billingStatus.days_left_in_trial > 0
                            ? `${billingStatus.days_left_in_trial} Days left in your trial.`
                            : 'Trial expired.'
                        }
                    </p>
                    <div className="text-center">
                        <a
                            href="/billing"
                            className="btn btn-sm text-white bg-danger"
                        >
                            Click to Upgrade
                        </a>
                    </div>
                </>
            );
        }

        // Case 2: User is subscribed but subscription is canceled
        if (billingStatus.is_subscribed && billingStatus.subscription_canceled) {
            return (
                <>
                    <h3 className="text-center"><b>Subscription canceled</b></h3>
                    <p className="mb-3 text-center">
                        {billingStatus.days_left_in_grace_period !== null && billingStatus.days_left_in_grace_period > 0
                            ? `${billingStatus.days_left_in_grace_period} Days left in your plan.`
                            : 'Grace period expired.'
                        }
                    </p>
                    <div className="text-center">
                        <a
                            href="/billing"
                            className="btn btn-sm text-white bg-danger"
                        >
                            Click to Resume
                        </a>
                    </div>
                </>
            );
        }

        // Case 3: Trial ended and not subscribed (from Blade template @else case)
        if (!billingStatus.on_trial && !billingStatus.is_subscribed) {
            return (
                <>
                    <h3 className="text-center"><b>Trial Plan</b></h3>
                    <p className="mb-3 text-center">Trial expired.</p>
                    <div className="text-center">
                        <a
                            href="/billing"
                            className="btn btn-sm text-white bg-danger"
                        >
                            Click to Upgrade
                        </a>
                    </div>
                </>
            );
        }

        return null;
    };

    return (
        <div className="mt-5 billing-timer">
            <div className="position-relative">
                <div 
                    className={`billing-custom-css ${getVariantClasses()} mx-auto d-none d-lg-block`}
                    style={{ zIndex: 1000, position: 'absolute' }}
                >
                    <div></div>
                    <div className="mb-3 text-center">
                        <ClockIcon />
                    </div>
                    {renderContent()}
                </div>
            </div>
        </div>
    );
};

export default BillingTimer;
