<?php

use App\Http\Controllers\Api\Attribute\AttributeController;
use App\Http\Controllers\Api\Brand\BrandController;
use App\Http\Controllers\Api\Category\CategoryController;
use App\Http\Controllers\Api\Channel\ChannelController;
use App\Http\Controllers\Api\Family\FamilyController;
use App\Http\Controllers\Api\File\FileController;
use App\Http\Controllers\Api\Filter\FilterController;
use App\Http\Controllers\Api\Product\ProductController;
use App\Http\Controllers\Api\Marketing\HubspotController;
use App\Http\Controllers\Api\OrganizationController;
use App\Http\Controllers\Api\Vendor\VendorController;
use App\Http\Controllers\Api\Version\VersionController;
use App\Http\Controllers\Api\BrandsPortal\BrandsPortalController;
use App\Http\Controllers\Api\BrandsPortal\BrandsPortalProductsController;
use App\Http\Controllers\Api\Invite\TeamInviteController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\Location\LocationController;
use App\Http\Controllers\Api\Store\StoreController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Product\VariantController;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// dd(Auth::user()->createToken('api-token')->plainTextToken); use for generating token
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::namespace('Api')->group(function () {
    Route::prefix('product')->namespace('Product')->group(function () {
        Route::middleware(['auth:sanctum'])->group(function () {
            Route::post('versions/variants/create', 'VariantController@createVariant')->name('api.variants.create');
            Route::get('attributes/variants/options/{product_id}/{version_id}', 'VariantController@getAttributeVariantsOptions');
            Route::get('{product_id}/versions/{version_id}/variants', 'VariantController@getAttributeVariants');
            Route::put('versions/variants/update', 'VariantController@updateAttributeVariants')->name('api.variants.update');
            Route::delete('variants/delete', 'VariantController@delete_variants')->name('api.variants.delete');
        });
    });
});

// Route::middleware(['restricturlaccess'])->group(function () {
/*
|--------------------------------------------------------------------------
| Version 2.0.0
|--------------------------------------------------------------------------
|
| Version 2.0.0 known as V2 routes can be accessed with below routes.
|
*/
Route::prefix('2024-12')->group(function () {

    /*
|--------------------------------------------------------------------------
| Apimio Api Authentication Routes
|--------------------------------------------------------------------------
|
|
*/
    Route::post('login', [AuthController::class, 'login']);
    Route::middleware('auth:sanctum')->post('login-organization', [AuthController::class, 'loginOrganization']);
    /*
|--------------------------------------------------------------------------
| Product version CRUD API
|--------------------------------------------------------------------------
|
| Product version CRUD Api routes.
|
*/
    Route::apiResource('versions', VersionController::class);

    /*
|--------------------------------------------------------------------------
| Product vendor CRUD API
|--------------------------------------------------------------------------
|
| Product vendor CRUD Api routes.
|
*/
    Route::apiResource('vendors', VendorController::class);

    /*
|--------------------------------------------------------------------------
| Product category CRUD API
|--------------------------------------------------------------------------
|
| Product category CRUD Api routes.
|
*/
    Route::apiResource('categories', CategoryController::class);
    /*
|--------------------------------------------------------------------------
| Product brand CRUD API
|--------------------------------------------------------------------------
|
| Product brand CRUD Api routes.
|
*/
    Route::apiResource('brands', BrandController::class);
    /*
|--------------------------------------------------------------------------
| Product channel CRUD API
|--------------------------------------------------------------------------
|
| Product channel CRUD Api routes.
|
*/
    Route::apiResource('channels', ChannelController::class);


    /*
|--------------------------------------------------------------------------
| Product family CRUD API
|--------------------------------------------------------------------------
|
| Product family CRUD Api routes.
|
*/
    Route::apiResource('families', FamilyController::class);

    /*
|--------------------------------------------------------------------------
| Product attribute CRUD API
|--------------------------------------------------------------------------
|
| Product attribute CRUD Api routes.
|
*/
    Route::apiResource('attributes', AttributeController::class);

    /*
|--------------------------------------------------------------------------
| Product product CRUD API
|--------------------------------------------------------------------------
|
| Product product CRUD Api routes.
|
*/
    Route::apiResource('products', ProductController::class)->except(['index']);
    Route::post('products/index', [ProductController::class, 'index']);
//    Route::get('products/index', [ProductController::class, 'index']);


    /*
|--------------------------------------------------------------------------
| Product Variants CRUD API
|--------------------------------------------------------------------------
|
| Product Variants CRUD Api routes.
|
*/
    //    Route::group(['prefix' => 'variant', 'namespace' => 'Product', 'as' => 'variant'], function () use ($router) {
    //
    //        Route::post('store', ["uses" => 'VariantController@store', "as" => "store"]);
    //
    //    });


    /*
|--------------------------------------------------------------------------
| Product file CRUD API
|--------------------------------------------------------------------------
|
| Product file CRUD Api routes.
|
*/
    Route::apiResource('files', FileController::class);
    Route::post('files/attach', FileController::class . '@AttachFileToProduct');

    /*
|--------------------------------------------------------------------------
| Filter Attributes CRUD API
|--------------------------------------------------------------------------
|
| Filter Attribute CRUD Api routes.
|
*/
    Route::apiResource('filters', FilterController::class);

    /*
|--------------------------------------------------------------------------
|  Brand Portal CRUD API
|--------------------------------------------------------------------------
|
|  Brand Portal CRUD Api routes.
|
*/
    Route::apiResource('brands-portals', BrandsPortalController::class)
        ->except(['update']);
    Route::post('brands-portal/{id}', [BrandsPortalController::class, 'update']);
    Route::get('brands-portal/{id}', [BrandsPortalController::class, 'edit']);
    /*
|--------------------------------------------------------------------------
|  Brand Portal products CRUD API
|--------------------------------------------------------------------------
|
|  Products CRUD Api routes.
|
*/

    Route::apiResource('brands-portal-products', BrandsPortalProductsController::class);

    Route::middleware(['throttle:brandExport'])->group(function () {
        Route::post('brands-portal-products/export', [BrandsPortalProductsController::class, 'export']);
    });

    /*
|--------------------------------------------------------------------------
|  Hubspot CRUD API
|--------------------------------------------------------------------------
|
|  Hubspot CRUD Api routes.
|
*/
    Route::apiResource('hubspot', HubspotController::class);

    /*
|--------------------------------------------------------------------------
|  Organization CRUD API
|--------------------------------------------------------------------------
|
|  Organizations CRUD Api routes.
|
*/
    Route::get('organization/billing/status', [OrganizationController::class, 'billingStatus']);
    Route::apiResource('organization', OrganizationController::class);

    /*
|--------------------------------------------------------------------------
|  Invites CRUD API
|--------------------------------------------------------------------------
|
|  Invites CRUD Api routes.
|
*/
    Route::apiResource('invite', TeamInviteController::class);
    Route::get('invite-permissions', [TeamInviteController::class, 'permissions']);


    /*
|--------------------------------------------------------------------------
| Dashboard API
|--------------------------------------------------------------------------
|
| Dashboard Api routes.
|
*/
    Route::apiResource('dashboard', DashboardController::class);
    //returns all products quality score
    Route::get('product-quality-score', [DashboardController::class, 'ProductQualityScore']);
    //returns images quality score
    Route::get('image-quality-score', [DashboardController::class, 'ImageQualityScore']);
    //return all products sync status
    Route::get('shopify-sync-status', [DashboardController::class, 'shopifySyncStatus']);

    //return unread notifications count
    Route::get('notifications/unread-count', function () {
        if (!auth()->check()) {
            return response()->json(['count' => 0]);
        }

        $count = auth()->user()->unreadNotifications
            ->where('organization_id', auth()->user()->organization_id)
            ->count();

        return response()->json(['count' => $count]);
    });

    /*
|--------------------------------------------------------------------------
|  Location CRUD API
|--------------------------------------------------------------------------
|
|  Location CRUD Api routes.
|
*/
    Route::apiResource('location', LocationController::class);





});
